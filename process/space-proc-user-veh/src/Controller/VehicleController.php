<?php

namespace App\Controller;

use App\Helper\ErrorResponse;
use App\Model\MyMarqueVehicleModel;
use App\Model\VehicleModel;
use App\Manager\MyMarqueVehicleManager;
use App\Manager\VehicleManager;
use App\Trait\ValidationResponseTrait;
use Nelmio\ApiDocBundle\Attribute\Model;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('v1/vehicles', name: 'user_vehicle_')]
class VehicleController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: 'list', methods: ['GET'])]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'userId',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'Vehicles')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new Model(type: VehicleModel::class, groups: ['list'])
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    public function index(ValidatorInterface $validator, VehicleManager $vehicleManager, Request $request): JsonResponse
    {
        $userId = '';

        $errors = $validator->validate(
            compact('userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (! empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }
        $response = $vehicleManager->getVehicles($userId)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('', name: 'new', methods: ['POST'])]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'userId', type: 'string', required: ['true']),
                new OA\Property(property: 'vin', type: 'string'),
                new OA\Property(property: 'brand', type: 'string'),
                new OA\Property(property: 'visual', type: 'string'),
                new OA\Property(property: 'culture', type: 'string'),
                new OA\Property(property: 'label', type: 'string'),
                new OA\Property(property: 'versionId', type: 'string'),
                new OA\Property(property: 'mopId', type: 'string'),
                new OA\Property(property: 'orderFormId', type: 'string'),
                new OA\Property(property: 'trackingStatus', type: 'string'),
                new OA\Property(property: 'orderFormStatus', type: 'string'),
            ]
        )
    )]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'userId', default: '001xf5d49f0b4dce874859657ae98122'),
                new OA\Property(property: 'vin', default: 'VR3UPHNKSKT101600'),
                new OA\Property(property: 'brand', default: 'OP'),
                new OA\Property(property: 'visual', default: ''),
                new OA\Property(property: 'culture', default: 'en_GB'),
                new OA\Property(property: 'label', default: 'Order OPEL Vehicle'),
                new OA\Property(property: 'versionId', default: '1PP2A5HMT1B0A4B0'),
                new OA\Property(property: 'mopId', default: '001bZjM3ODBjZTNlNDY3MGNmNTg4AAAA'),
                new OA\Property(property: 'orderFormId', default: '566777'),
                new OA\Property(property: 'trackingStatus', default: 'PRODUCTION_START'),
                new OA\Property(property: 'orderFormStatus', default: 'TO_BE_VALIDATED'),
            ]
        )
    )]
    #[OA\Tag(name: 'Vehicles')]
    public function VehicleOrder(ValidatorInterface $validator, Request $request, VehicleManager $vehicleManager): JsonResponse
    {
        $content = json_decode($request->getContent(), true);
        $culture = $content['culture'] ?? '';

        $errors = $validator->validate(
            compact('content', 'culture'),
            new Assert\Collection([
                'content' => new Assert\NotBlank(),
                'culture' => [new Assert\NotBlank(), new Assert\Locale()],
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (! empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $vehicleManager->createOrUpdateVehicle($content)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/{id}/summary', name: 'summary', methods: ['GET'])]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'vehicleId',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'user id',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'Vehicles')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'success', properties: [
                new OA\Property(property: 'url'),
            ])]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    public function VehicleSummary(ValidatorInterface $validator, VehicleManager $vehicleManager, Request $request, string $id): JsonResponse
    {
        $userId = $request->headers->get('userId') ?? '';

        $errors = $validator->validate(
            compact('id', 'userId'),
            new Assert\Collection([
                'id' => new Assert\NotBlank(),
                'userId' => new Assert\NotBlank(),
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (! empty($messages)) {
            $response = $this->getValidationErrorResponse($messages)->toArray();

            return $this->json($response['content'], $response['code']);
        }

        $response = $vehicleManager->getVehicleSummary($id, $userId)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/add', name: 'addVehicleToUserGarage', methods: ['POST'])]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        required: true,
        allowEmptyValue: false,
        description: 'Brand',
        example: 'OP',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        required: true,
        allowEmptyValue: false,
        description: 'Country, always uppercase',
        example: 'FR',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        required: true,
        allowEmptyValue: false,
        description: 'Language, always lowercase',
        example: 'en',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        required: false,
        description: 'Source',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(
                    property: 'vin',
                    description: 'VIN code',
                    type: 'string',
                    example: 'VR3UPHNKSKT101600'
                ),
                new OA\Property(
                    property: 'mileage',
                    description: 'Mileage value for the vehicle; it is a string, but must be rapresent a positive integer; zero also is a valid value',
                    type: 'string',
                    example: '0'
                ),
            ]
        )
    )]
    #[OA\Tag(name: 'Vehicles')]
    #[OA\Response(
        response: 200,
        description: 'Successful response, no vehicles needed to be added',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'success', properties: [
                new OA\Property(property: 'vin', type: 'string', example: 'VR3UPHNKSKT101600'),
            ]
            )]
        )
    )]
    #[OA\Response(
        response: 201,
        description: 'Successful response, new vehicles added to the user garage',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'success', properties: [
                new OA\Property(property: 'vin', type: 'string', example: 'VR3UPHNKSKT101600'),
            ]
            )]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
    )]
    #[OA\Response(
        response: 500,
        description: 'An Internal Server Error code is returned if some unmanaged error occurs',
    )]
    public function addVehicle(Request $request, MyMarqueVehicleManager $myMarqueVehicleManager): JsonResponse
    {
        try {
            $content = json_decode($request->getContent(), true);

            $acnt = $request->headers->get('userId') ?? '';
            $vin = $content['vin'] ?? '';
            $model = new MyMarqueVehicleModel(
                acnt: $acnt,
                vin: $vin,
                mileage: $content['mileage'] ?? '',
                brand: (string) $request->query->get('brand', ''),
                country: (string) $request->query->get('country', ''),
                language: (string) $request->query->get('language', ''),
                checkActivation: true,
                source: (string) $request->query->get('source', '')
            );
            $response = $myMarqueVehicleManager->addVehicleToUserGarage($model);

            if ($response instanceof ErrorResponse) {
                return $this->json(['error' => ['message' => $response->getMessage()]], Response::HTTP_BAD_REQUEST);
            }

            if (Response::HTTP_CREATED === $response->getCode()) {
                return $this->json(['success' => ['vin' => $vin]], Response::HTTP_CREATED);
            }

            return $this->json(['success' => ['vin' => $vin]], Response::HTTP_OK);
        } catch (\Throwable $e) {
            return $this->json(['error' => ['message' => $e->getMessage()]], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
