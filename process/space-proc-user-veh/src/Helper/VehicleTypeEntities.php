<?php

namespace App\Helper;

class VehicleTypeEntities
{
    const TYPES = [
        '00' => 'ICE',
        '02' => 'HEV',
        '03' => 'PHEV',
        '04' => 'BEV',
        '05' => 'MHEV',
        '06' => 'HFCV',
    ];

    // used for feature codes based on the above defined types
    const ENGINE_TYPES = [
        'ICE' => 'ICE',
        'HEV' => 'HEV',
        'PHEV' => 'PHEV',
        'BEV' => 'BEV',
        'MHEV' => 'MHEV',
        'HFCV' => 'HYDROGEN'
    ];

    const PLUG_TYPES = [
        '00' => null,
        '01' => 'CCS1',
        '02' => 'CCS2',
        '03' => 'GBT',
        '04' => 'CHADEMO',
        '05' => 'Type1',
        '06' => 'Type2',
    ];

    public static function getType(string $attributCode, array $corvetAttributes = [])
    {
        foreach ($corvetAttributes as $value) {
            $rest = substr($value, 0, 3);
            if($rest === $attributCode){
                return substr($value, 3, 2);
            }
        }
        return -1;
    }
}
