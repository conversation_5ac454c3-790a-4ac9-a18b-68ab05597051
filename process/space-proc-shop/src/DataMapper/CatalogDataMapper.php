<?php

declare(strict_types=1);

namespace App\DataMapper;

use phpDocumentor\Reflection\PseudoTypes\True_;

/**
 * Class ContribsTransformer
 * @package App\DataTransformers
 */
class CatalogDataMapper
{
    /**
     * @var array
     */
    protected $item;

    public function setItem(array $item)
    {
        $this->item = $item;

        return $this;
    }

    /**
     * @param  array  $data
     *
     * @return array
     */
    public function transform(array $data): array
    {
        return [
            'catalogId'               => $this->item['id'],
            'target'                  => "B2C",
            'title'                   => $data['title'],
            'shortDescription'        => $data['shortDescription'] ?? '',
            'fullDescription'         => $data['fullDescription'] ?? '',
            'topMainImage'            => $data['topMainImage'] ?? '',
            'familyName'              => $this->item['familyName'] ?? '',
            'isBundle'                => true,
            'offers'                  => $this->getOffers(),
            'tnc'                     => $this->getTnc($data),
            'consentsLabel'           => $data['consents']['LongConsentDesc'] ?? '',
            'disclaimerLabel'         => $data['disclaimerLabel'] ?? '',
            'standaloneProducts'      => $this->getStandaloneProducts($data)
        ];
    }

    public function getOffers(): array
    {
        $offers = [];
        
        if (isset($this->item['offers']) && is_array($this->item['offers'])) {
            foreach ($this->item['offers'] as $offer) {
                $prices = [];
                
                if (isset($offer['prices']) && is_array($offer['prices'])) {
                    foreach ($offer['prices'] as $price) {
                        $prices[] = [
                            'id' => $price['id'] ?? '',
                            'currency' => $price['currency'] ?? '',
                            'price' => $price['price'] ?? '',
                            'typeDiscount' => $price['typeDiscount'] ?? '',
                            'periodType' => $price['periodType'] ?? ''
                        ];
                    }
                }
                
                $offers[] = [
                    'id' => $offer['id'] ?? '',
                    'duration' => $offer['duration'] ?? '',
                    'effectiveStartDate' => $offer['effectiveStartDate'] ?? '',
                    'effectiveEndDate' => $offer['effectiveEndDate'] ?? '',
                    'priority' => $offer['priority'] ?? null,
                    'isFreeTrial' => $offer['isFreeTrial'] ?? null,
                    'freeTrialDuration' => $offer['freeTrialDuration'] ?? '',
                    'freeTrialDurationType' => $offer['freeTrialDurationType'] ?? '',
                    'category' => $offer['category'] ?? '',
                    'fromPrice' => $offer['fromPrice'] ?? null,
                    'durationType' => $offer['durationType'] ?? '',
                    'isBundleOffer' => $offer['isBundleOffer'] ?? null,
                    'prices' => $prices
                ];
            }
        }

        return $offers;
    }

    public function getTnc(array $data): array
    {
        if (isset($data['tnc'])) {
            $tnc = $data['tnc'];
            return [
                'version' => $tnc['version'] ?? '',
                'tncLabel' => $tnc['title'] ?? '',
                'id' => $tnc['id'] ?? '',
                'tncUrl' => $tnc['tncUrl'] ?? ''
            ];
        }
        
        return [
            'version' => '',
            'tncLabel' => '',
            'id' => '',
            'tncUrl' => ''
        ];
    }

    public function getStandaloneProducts(array $data): array
    {
        $standaloneProducts = [];
        
        if (isset($data['standaloneProducts']) && is_array($data['standaloneProducts'])) {
            foreach ($data['standaloneProducts'] as $product) {
                $standaloneProducts[] = [
                    'productLabel' => $product['features'][0]['full_desc'] ?? '',
                    'icon' => $product['features'][0]['icon'] ?? ''
                ];
            }
        }
        return $standaloneProducts;
    }
}
