<?php

namespace App\Controller;

use Nelmio\ApiDocBundle\Annotation\Model;
use OpenApi\Attributes as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes\JsonContent;

use App\Model\CatalogModel;
use App\Trait\ValidationResponseTrait;
use App\Validator\VinValidator;
use App\Validator\BrandValidator;
use App\Validator\CountryValidator;
use App\Validator\LanguageValidator;
use App\Model\SubscriptionModel;
use App\Manager\SubscriptionManager;

#[Route('/v1/subscription', name: 'Subscription_')]
class SubscriptionController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('', name: 'getSubscription', methods: ['GET'])]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new Model(type: SubscriptionModel::class)
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 404,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Bad Request::Validation Error: Required Fields Missing',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                new OA\Property(property: 'errors', type: 'object', properties: [
                    new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                ])
            ])
        ])
    )]    
    #[OA\Response(
        response: 500,
        description: 'Internal Server Error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'target',
        in: 'query',
        description: 'target parameter',
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Tag(name: 'SUBSCRIPTION API')]
    public function getSubscription(Request $request, ValidatorInterface $validator, SubscriptionManager $subscriptionManager): JsonResponse
    {
        $userId = $request->headers->get('userId') ?? '';
        $vin = $request->headers->get('vin') ?? '';
        $target = $request->query->get('target') ?? '<string>';
        $errors = $validator->validate(
            compact('vin', 'userId'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => VinValidator::getConstraints()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }
        $response = $subscriptionManager->getSubscription($userId, $vin, $target)->toArray();
        return $this->json($response['content'], $response['code']);
    }

    #[Route('/cart/items/checkout', name: 'cartItemsCheckout', methods: ['POST'])]
    #[OA\Response(
        response: 200,
        description: 'Successful Response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message', type: 'string'),
                    new OA\Property(property: 'data', type: 'object'),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Bad Request::Validation Error: Required Fields Missing',
        content: new JsonContent(type: 'object', properties: [
            new OA\Property(property: 'error', properties: [
                new OA\Property(property: 'message', type: 'string', example: 'validation_failed'),
                new OA\Property(property: 'errors', type: 'object', properties: [
                    new OA\Property(property: 'userId', type: 'string', example: 'This value should not be blank.')
                ])
            ])
        ])
    )]    
    #[OA\Response(
        response: 500,
        description: 'Internal Server Error',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Parameter(
        name: 'userId',
        in: 'header',
        description: 'User ID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'vin',
        in: 'header',
        description: 'VIN',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'country',
        in: 'query',
        description: 'Country',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'language',
        in: 'query',
        description: 'Language',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        description: 'Source',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'target',
        in: 'query',
        description: 'Target',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        required: true,
        content: new JsonContent(
            type: 'object',
            required: ['vehicleDescription', 'items'],
            properties: [
                new OA\Property(
                    property: 'vehicleDescription', 
                    type: 'string',
                    description: 'Description of the vehicle',
                    example: 'DS3 xxxxx'
                ),
                new OA\Property(
                    property: 'items',
                    type: 'array',
                    description: 'List of subscription items',
                    items: new OA\Items(
                        type: 'object',
                        required: ['type', 'id', 'tncVersion', 'tncId', 'tncUrl'],
                        properties: [
                            new OA\Property(property: 'type', type: 'string', example: 'zuoraRatePlan'),
                            new OA\Property(property: 'id', type: 'string', example: '8adc8f99697a22240169916559841311'),
                            new OA\Property(property: 'tncVersion', type: 'string', example: '1.4'),
                            new OA\Property(property: 'tncId', type: 'string', example: '162'),
                            new OA\Property(
                                property: 'tncUrl', 
                                type: 'string', 
                                format: 'url',
                                example: 'https://connect.opel.de/sites/ov/files/2024-08/DE_TC_Connect%20Plus_06_2024_7.pdf'
                            )
                        ]
                    )
                )
            ]
        )
    )]
    #[OA\Tag(name: 'SUBSCRIPTION API')]
    public function cartItemsCheckout(Request $request, ValidatorInterface $validator, SubscriptionManager $subscriptionManager): JsonResponse
    {
        $userId = $request->headers->get('userId') ?? '';
        $vin = $request->headers->get('vin') ?? '';
        $brand = $request->query->get('brand') ?? '';
        $country = $request->query->get('country') ?? '';
        $language = $request->query->get('language') ?? '';
        $source = $request->query->get('source') ?? '';
        $target = $request->query->get('target') ?? '';
        $content = json_decode($request->getContent(), true);

        $errors = $validator->validate(
            compact('userId', 'vin', 'brand', 'country', 'language', 'source', 'target'),
            new Assert\Collection([
                'userId' => new Assert\NotBlank(),
                'vin' => VinValidator::getConstraints(),
                'brand' => BrandValidator::getConstraints(),
                'country' => CountryValidator::getConstraints(),
                'language' => LanguageValidator::getConstraints(),
                'source' => new Assert\NotBlank(),
                'target' => new Assert\NotBlank()
            ])
        );
        
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }
        
        $response = $subscriptionManager->cartItemsCheckout($userId, $vin, $brand, $country, $language, $source, $target, $content)->toArray();
        return $this->json($response['content'], $response['code']);
    }
}
