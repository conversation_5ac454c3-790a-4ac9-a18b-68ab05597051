<?php

namespace App\Service;

use App\Exception\CorvetException;
use App\Exception\VehicleNotFoundException;
use App\Trait\LoggerTrait;
use Exception;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CorvetService
{
    const TYPES = [
        'DXD04CD' => 'ELECTRIC',
        'DXD03CD' => 'HYBRID',
        'DXD06CD' => 'HYDROGEN',
    ];

    const DRE_TYPES = [
        'DRE20CD' => 'heating_temp_regulation',
        'DRE22CD' => 'manual_temp_regulation',
        'DRE24CD' => 'automatic_temp_regulation',
    ];
    
    use LoggerTrait;

    /**
     * @param CorvetSysConnector $corvetSysConnector
     */
    public function __construct(
        private CorvetSysConnector $corvetSysConnector
    ) {
    }

    public function getCorvetResponse($brand, $country, $vin, $source = "APP")  
    {
        $this->logger->info("Call corvet for $vin");
        $uri = "/v1/corvet/{$vin}/data";
        $response = $this->corvetSysConnector->call(Request::METHOD_GET, $uri, compact('brand'), compact('brand'));

        if ($response->getStatusCode() != Response::HTTP_OK) {
            throw CorvetException::make();
        }

        $corvetResponse = $response->toArray(false);
        return $corvetResponse['success'] ?? [];
    }

    /**
     * @param $brand
     * @param $country
     * @param $vin
     * @param string $source
     * @param bool $withAttributes
     * @return array
     */
    public function getLcdv($brand, $country, $vin, $source = "APP", $withAttributes = false)
    {
        try {
            $this->logger->info('Get vehicle Data for vin ' . $vin);
            $vin = strtoupper($vin);
            $brand = strtoupper($brand);
            $country = strtoupper($country);
            $source = strtoupper($source);
            $response = $this->getCorvetResponse($brand, $country, $vin, $source);

            if (!$this->checkVehicleExists($response) || !isset($response['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'])) {
                $this->logger->error('Vehicle NOT FOUND ' . $vin . '(' . json_encode($response) . ')');
                return $this->_buildErrorResponse(Response::HTTP_NOT_FOUND, "Vehicle Not Found");
            }
            $allAttributes = $response['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            if (!is_array($allAttributes)) {
                $allAttributes = [$allAttributes];
            }
            $attributesData = $this->getDataFromAttributes($allAttributes);
            $attributes = $attributesData['managed_attributes'] ?? [];
            $vehicleData = $response['VEHICULE']['DONNEES_VEHICULE'];
            $getVehicleType =  $this->getType($attributes);
            $vehicleType = ($getVehicleType != -1) ? $getVehicleType : 0;
            $data =  [
                "lcdv" => $vehicleData['LCDV_BASE'],
                "date" => $vehicleData['DATE_ENTREE_COMMERCIALISATION'],
                "warranty_start_date" => $vehicleData['DATE_DEBUT_GARANTIE'] ? $vehicleData['DATE_DEBUT_GARANTIE'] : '',
                "types"  => $this->getVehicleTypes($attributes),
                'type'   =>  $vehicleType,
                'naPvPr' => $vehicleData['N_APV_PR'] ?? '',
                'modelYear' => $vehicleData['ANNEE_MODELE'] ?? '',
                'options' => $attributesData['vehicle_options'] ?? [],
                "additionnal_attributes" => $this->getAdditionalAttributes($vehicleData['LCDV_BASE'] , $allAttributes)
            ];
            if ($withAttributes) {
                $data['attributes'] = $attributes;
                $data['all_attributes'] = $allAttributes;
            }
            return $data;
        } catch (Exception $e) {
           
            $this->logger->error('Error: Get vehicle Data for vin ' . $vin . '(' . $e->getMessage() . ')');
            throw CorvetException::make();
        }
    }

    /**
     * @param array $response
     * @return bool
     */
    private function checkVehicleExists(array $response)
    {
        return ($response['VEHICULE']['@attributes']['Existe'] ?? '') == 'O';
    }

    private function getDataFromAttributes(array $attributes)
    {
        $managedAttributes = [];
        $response  = ['vehicle_options' => []];
        foreach ($attributes as $attribute) {
            if (preg_match('/^P(.{4})|^D(.{4})CP$/i', $attribute)) {
                $response['vehicle_options'][] = substr($attribute, 1, 4);
            }

            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                default:
                    break;
            }
        }
        $response['managed_attributes'] = $managedAttributes;

        return $response;
    }

    public function getManagedAttributes(array $attributes): ?array
    {
        $managedAttributes  = [];
        foreach ($attributes as $attribute) {
            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DO9':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D32':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DYR':
                    if (substr($attribute, 3, 2) == "17") {
                        $managedAttributes[] = trim($attribute);
                    }
                    break;
            }
        }
        return $managedAttributes;
    }

    // VehicleType
    public static function getVehicleTypes($corvetAttributes)
    {
        $types = [];
        array_walk($corvetAttributes, function($value) use (&$types) {
            if (isset(self::TYPES[trim($value)])) {
                $types[] = self::TYPES[trim($value)];
            }
        });
        return array_unique($types);
    }

    /**
     * Get the number of the type of the vehicle
     * @param $corvetAttributes
     * @return int
     */
    public static function getType($corvetAttributes)
    {
        foreach ($corvetAttributes as $key => $value) {
            $rest = substr($value, 0, 3);
            if($rest === 'DXD'){
                return (int)substr($value, 3, 2);
            }
        }
        return -1;
    }
    
    /**
     * VehicleType
     * getAdditionalAttributes
     *
     * @param array $corvetAttributes
     * @return array
     */
    public static function getAdditionalAttributes(string $lcdv , array $corvetAttributes)
    {
        $supportedTypes = ['DVQ', 'DAR'];

        $result = [
            'b0f' => substr($lcdv, 7, 2)
        ];
        foreach ($corvetAttributes as $value) {
            $name = substr($value, 0, 3);
            if (in_array($name,$supportedTypes)) {
                $result[strtolower($name)] = substr($value, 3, 2);
            }
        }
        return $result;
    }

    /**
     * Build error response
     * @param $code
     * @param $content
     * @return array
     */
    private function _buildErrorResponse($code, $content)
    {
        return [
            'error' => [
                'code' => $code,
                'content' => $content
            ],
            'success' => false
        ];
    }

}
