# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    cert_dir: '%kernel.project_dir%/config/cert'
    domtoms: ['GP', 'MQ', 'RE', 'GF']
    account:
        region:  'eu-west-1' #'%env(AWS_DEFAULT_REGION)%'
        version: 'latest'

imports:
    - { resource: 'sams_parameters.yaml' }
    - { resource: 'cache_parameters.yaml' }

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $settingsUrl: "%env(MYM_SETTINGS_URL)%"
            $cacheDurations: "%cache_durations%"
            $timeouts: "%request_timeouts%"
            $certifDir: "%cert_dir%"
            $domtoms: "%domtoms%"
            $urlV2: "%sams.v2.url%"
            $contribUrlV2: "%sams.v2.contrib_url%"
            $certNameV2: "%sams.v2.cert%"
            $authHttpLoginV2: "%sams.v2.login%"
            $authHttpPasswordV2: "%sams.v2.password%"
            $cartUrlV2: "%sams.v2.cart%"
    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
            - '../src/Tests/'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller/'
        tags: ['controller.service_arguments']
    
    sensio_framework_extra.view.listener:
        alias: Sensio\Bundle\FrameworkExtraBundle\EventListener\TemplateListener
    
    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    Symfony\Component\Cache\Adapter\TagAwareAdapter:
        arguments: ['@cache.app', '@cache.app']
        
    App\EventListener\ExceptionListener:
        tags:
            - { name: kernel.event_listener, event: kernel.exception }
    App\Service\EncryptorService:
        arguments:
            $secret: "%env(DCP_SECRET)%"
            $vector: "%env(DCP_VECTOR)%"

    # Configure SubscriptionService with explicit HTTP client dependencies
    App\Service\SubscriptionService:
        arguments:
            $httpClient: '@App\Connector\HttpClientWithCacheTimeouter'
            $httpClientSimple: '@App\Connector\HttpClient'