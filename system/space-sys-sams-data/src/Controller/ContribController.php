<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use OpenApi\Attributes as OA;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Model;

use App\Trait\ValidationResponseTrait;
use App\Helper\ErrorResponse;
use App\Validator\BrandValidator;
use App\Validator\Culture;
use App\Model\VehicleInfo;
use App\Manager\ContribManager;

#[Route('v1/sams', name: 'catalog_')]
class ContribController extends AbstractController
{
    use ValidationResponseTrait;

    #[Route('/contrib/getInfoByProduct/{productId}', methods: ['GET'])]
    #[OA\Parameter(
        name: 'productId',
        in: 'path',
        description: 'Product Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'culture',
        in: 'query',
        description: 'Culture',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        description: 'Source',
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'WEB', 'SPACEWEB']
        ),
        required: true
    )]
    #[OA\Tag(name: 'SAMS : Catalog')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getContribInfoByProduct(string $productId, Request $request, ContribManager $contribManager, ValidatorInterface $validator): JsonResponse
    {
        $brand = strtoupper(trim($request->query->get('brand', '')));
        $culture = trim($request->query->get('culture', ''));
        $source = trim($request->query->get('source', 'APP'));
        $errors = $validator->validate(
            compact('brand', 'culture'),
            new Assert\Collection([
                'brand'    => BrandValidator::getConstraintsForBrandCodeOrId(),
                'culture' => new Culture()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }

        $response = $contribManager->getContribDataByProduct($brand, $culture, $productId, $source)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/contrib/getInfoByProduct', methods: ['GET'])]
    #[OA\Parameter(
        name: 'productIds',
        in: 'query',
        description: 'Product Ids',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'culture',
        in: 'query',
        description: 'Culture',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        description: 'Source',
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'WEB', 'SPACEWEB']
        ),
        required: true
    )]
    #[OA\Tag(name: 'SAMS : Catalog')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success')
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getContribInfoByProductIds(Request $request, ContribManager $contribManager, ValidatorInterface $validator): JsonResponse
    {
        $productIds = trim($request->query->get('productIds', ''));
        $brand = strtoupper(trim($request->query->get('brand', '')));
        $culture = trim($request->query->get('culture', ''));
        $source = trim($request->query->get('source', 'APP'));
        $errors = $validator->validate(
            compact('brand', 'culture', 'productIds'),
            new Assert\Collection([
                'brand'    => BrandValidator::getConstraintsForBrandCodeOrId(),
                'culture' => new Culture(),
                'productIds' => new Assert\NotBlank()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }

        $response = $contribManager->getContribDataByProductIds($brand, $culture, $productIds, $source)->toArray();

        return $this->json($response['content'], $response['code']);
    }

    #[Route('/contrib/content/{productId}', methods: ['GET'])]
    #[OA\Parameter(
        name: 'productId',
        in: 'path',
        description: 'Product Id',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'brand',
        in: 'query',
        description: 'Brand',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'culture',
        in: 'query',
        description: 'Culture',
        schema: new OA\Schema(type: 'string'),
        required: true
    )]
    #[OA\Parameter(
        name: 'source',
        in: 'query',
        description: 'Source',
        schema: new OA\Schema(
            type: 'string',
            enum: ['APP', 'WEB', 'SPACEWEB']
        ),
        required: true
    )]
    #[OA\Tag(name: 'SAMS : Catalog')]
    #[OA\Response(
        response: 200,
        description: 'Successful response',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'success', properties: [
                    new OA\Property(property: 'message'),
                ])
            ]
        )
    )]
    #[OA\Response(
        response: 400,
        description: 'Bad Request',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    #[OA\Response(
        response: 422,
        description: 'Unporcessable Content',
        content: new JsonContent(
            type: 'object',
            properties: [
                new OA\Property(property: 'error', properties: [
                    new OA\Property(property: 'message'),
                    new OA\Property(property: 'errors', properties: []),
                ]),
            ]
        )
    )]
    public function getContribContentByProduct(string $productId, Request $request, ContribManager $contribManager, ValidatorInterface $validator): JsonResponse
    {
        $brand = strtoupper(trim($request->query->get('brand', '')));
        $culture = trim($request->query->get('culture', ''));
        $source = trim($request->query->get('source', 'APP'));
        $errors = $validator->validate(
            compact('brand', 'culture'),
            new Assert\Collection([
                'brand'    => BrandValidator::getConstraintsForBrandCodeOrId(),
                'culture' => new Culture()
            ])
        );
        $messages = $this->getValidationMessages($errors);
        if (!empty($messages)) {
            $messages = $this->getValidationErrorResponse($messages)->toArray();
            return $this->json($messages['content'], $messages['code']);
        }

        $response = $contribManager->getContribContentByProduct($brand, $culture, $productId, $source)->toArray();

        return $this->json($response['content'], $response['code']);
    }

}
