<?php

namespace App\Manager;

use Exception;
use Symfony\Component\HttpFoundation\Response;

use App\Helper\BrandProvider;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\CultureParser;
use App\Helper\UrlFormater;
use App\Model\CatalogModel;
use App\Trait\LoggerTrait;
use App\Service\ContribService;


class ContribManager
{
    use LoggerTrait;

    /**
     * @var ContribService
     */
    private $service;

    /**
     * @var string[]
     */
    private $domtoms;

    const WS_PARAM_SECTION = 'api_sams';

    public function __construct(ContribService $service, ?array $domtoms = [])
    {
        $this->service = $service;
        $this->domtoms = $domtoms;
    }

    public function getContribDataByProduct(string $brand, string $culture, string $productId, ?string $source = 'APP'): ResponseArrayFormat
    {
        try {

            $this->logger->info("CatalogManager: Call get contrib by product for vin {$brand}/{$culture} : {$productId}");
            $cultureAndLanguage = CultureParser::getCountryAndLanguage($culture);
            $url = $this->service->getDefaultContribUrl();
            if ($url === null) {
                throw new Exception("Default Contrib URL is not configured");
            }
            $this->service->setUrl($url);

            if (in_array($cultureAndLanguage['country'], $this->domtoms)) {
                $culture = 'fr-FR';
            }
            $response = $this->service->getContribDataByProduct($brand, $culture, $productId);
            if (Response::HTTP_OK != $response->getCode()) {
                $this->logger->error("Failed: Call Contrib data by product for {$productId}/{$brand}/{$culture}");
                $statusCode = $response->getCode();
                $responseData = json_decode($response->getData());
                if (is_object($responseData) && $responseData instanceof \stdClass && isset($responseData->message)) {
                    $message = isset($responseData->message) ? $responseData->message : $responseData;
                    $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $message . 'for vin : {$vin}');
                    return new ErrorResponse($message, $statusCode);
                }
                $message = (isset(json_decode($responseData->error)->message)) ? json_decode($responseData->error)->message : $responseData;
                $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $message . 'for vin : {$vin}');
                return new ErrorResponse($message, $statusCode);
            }

            return new SuccessResponse($response->getData());

        } catch (Exception $e) {
            $this->logger->error("Error: Call Contrib data by product for {$productId}/{$brand}/{$culture} ({$e->getMessage()})");
            return new ErrorResponse($e->getMessage(), $e->getCode());
            ;
        }
    }

    public function getContribDataByProductIds(string $brand, string $culture, string $productIds, ?string $source = 'APP'): ResponseArrayFormat
    {
        try {

            $this->logger->info("CatalogManager: Call get contrib by productIds for vin {$brand}/{$culture} : {$productIds}");
            $cultureAndLanguage = CultureParser::getCountryAndLanguage($culture);
            $url = $this->service->getDefaultContribUrl();
            if ($url === null) {
                throw new Exception("Default Contrib URL is not configured");
            }
            $this->service->setUrl($url);

            if (in_array($cultureAndLanguage['country'], $this->domtoms)) {
                $culture = 'fr-FR';
            }
            $response = $this->service->getContribDataByProductIds($brand, $culture, $productIds);
            if (Response::HTTP_OK != $response->getCode()) {
                $this->logger->error("Failed: Call Contrib data by product for {$productIds}/{$brand}/{$culture}");
                $statusCode = $response->getCode();
                $responseData = json_decode($response->getData());
                if (is_object($responseData) && $responseData instanceof \stdClass && isset($responseData->message)) {
                    $message = isset($responseData->message) ? $responseData->message : $responseData;
                    $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $message . 'for vin : {$vin}');
                    return new ErrorResponse($message, $statusCode);
                }
                $message = (isset(json_decode($responseData->error)->message)) ? json_decode($responseData->error)->message : $responseData;
                $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $message . 'for vin : {$vin}');
                return new ErrorResponse($message, $statusCode);
            }

            return new SuccessResponse($response->getData());

        } catch (Exception $e) {
            $this->logger->error("Error: Call Contrib data by product for {$productIds}/{$brand}/{$culture} ({$e->getMessage()})");
            return new ErrorResponse($e->getMessage(), $e->getCode());
            ;
        }
    }

    public function getContribContentByProduct(string $brand, string $culture, string $productId, ?string $source = 'APP'): ResponseArrayFormat
    {
        try {

            $this->logger->info("CatalogManager: Call get contrib by product for vin {$brand}/{$culture} : {$productId}");
            $cultureAndLanguage = CultureParser::getCountryAndLanguage($culture);
            $url = $this->service->getDefaultContribUrl();
            if ($url === null) {
                throw new Exception("Default Contrib URL is not configured");
            }
            $this->service->setUrl($url);

            if (in_array($cultureAndLanguage['country'], $this->domtoms)) {
                $culture = 'fr-FR';
            }
            $response = $this->service->getContribContentByProduct($brand, $culture, $productId);
            if (Response::HTTP_OK != $response->getCode()) {
                $this->logger->error("Failed: Call Contrib content by product for {$productId}/{$brand}/{$culture}");
                $statusCode = $response->getCode();
                $responseData = json_decode($response->getData());
                if (is_object($responseData) && $responseData instanceof \stdClass && isset($responseData->message)) {
                    $message = isset($responseData->message) ? $responseData->message : $responseData;
                    $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $message . 'for vin : {$vin}');
                    return new ErrorResponse($message, $statusCode);
                }
                $message = (isset(json_decode($responseData->error)->message)) ? json_decode($responseData->error)->message : $responseData;
                $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $message . 'for vin : {$vin}');
                return new ErrorResponse($message, $statusCode);
            }

            return new SuccessResponse($response->getData());

        } catch (Exception $e) {
            $this->logger->error("Error: Call Contrib content by product for {$productId}/{$brand}/{$culture} ({$e->getMessage()})");
            return new ErrorResponse($e->getMessage(), $e->getCode());
            ;
        }
    }
}
