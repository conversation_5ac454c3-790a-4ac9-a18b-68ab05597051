<?php

namespace App\Service;

use Exception;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Serializer\SerializerInterface;

use App\Model\VehicleInfo;
use App\Helper\WSResponse;
use App\Helper\FileHelper;
use App\Helper\CultureParser;
use App\Model\CatalogModel;
use App\Trait\LoggerTrait;
use App\Connector\HttpClientWithCacheTimeouter;
use App\Helper\ResponseArrayFormat;

class CatalogService
{
    use LoggerTrait;

    /**
     * @var HttpClientWithCacheTimeouter
     */
    private $client;

    /**
     * @var array
     */
    private $cacheDurations;

    /**
     * @var array
     */
    private $timeouts;

    /**
     * @var string
     */
    private $certifDir;

    /**
     * @var string
     */
    private $certNameV2;

    /**
     * @var array
     */
    private $basicAuthV2;

    /**
     * @var string
     */
    private $urlV2;

    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var FileHelper
     */
    private $fileHelper;

    public function __construct(
        HttpClientWithCacheTimeouter $httpClient,
        array $cacheDurations,
        array $timeouts,
        string $certifDir,
        string $certNameV2,
        string $authHttpLoginV2,
        string $authHttpPasswordV2,
        string $urlV2,
        FileHelper $fileHelper,
        SerializerInterface $serializer
    ) {
        $this->client = $httpClient;
        $this->cacheDurations = $cacheDurations;
        $this->timeouts = $timeouts;
        $this->certifDir = $certifDir;
        $this->fileHelper = $fileHelper;
        $this->serializer = $serializer;
        $this->certNameV2 = $certNameV2;
        $this->basicAuthV2 = [$authHttpLoginV2, $authHttpPasswordV2];
        $this->urlV2 = $urlV2;
    }

    private function getCertifFile(string $certifName, string $extension): string
    {
        return $this->certifDir . '/' . $this->fileHelper->setExtension($certifName, $extension);
    }

    public function getAssessedProducts(array $vehicleInfo): WSResponse
    {
        $options =  [
            'headers' => [
                'Content-Type' => 'application/json',
                'brand' => $vehicleInfo['store']['origin'],
                'country' => $vehicleInfo['store']['country']
            ],
            'timeout' => $this->timeouts['product_assessed'],
            'local_cert' => $this->getCertifFile($this->certNameV2, '.pem'),
            'auth_basic' => $this->basicAuthV2,
            'json' => $vehicleInfo
        ];

        return $this->client->request(
            Request::METHOD_POST,
            $this->urlV2 . '/catalog',
            CacheInfosProvider::getCatalogCacheKeyForLastResponse($vehicleInfo['store']['origin'], $vehicleInfo['store']['country'], $vehicleInfo['vehicle'] ?? null),
            $this->cacheDurations['product_assessed'] ?? -1,
            $options,
        );
    }
}
